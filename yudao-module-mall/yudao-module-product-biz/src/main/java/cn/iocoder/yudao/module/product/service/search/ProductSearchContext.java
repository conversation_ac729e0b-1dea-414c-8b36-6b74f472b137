package cn.iocoder.yudao.module.product.service.search;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchDetailReqVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
@Slf4j
public class ProductSearchContext {

    /**
     * 商品搜索策略配置键
     */
    static final String PRODUCT_SEARCH_STRATEGY_KEY = "product.search.strategy";

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private ConfigApi configApi;

    private ProductSearchService productSearchService;

    @PostConstruct
    public void init() {
        // 从系统配置中获取当前使用的搜索策略
        String strategyType = configApi.getConfigValueByKey(PRODUCT_SEARCH_STRATEGY_KEY);
        if (strategyType == null) {
            strategyType = "oneBound"; // 默认使用爬虫策略
            log.warn("[init] 未配置商品搜索策略，使用默认策略: {}", strategyType);
        }
        setProductSearchService(strategyType);
        log.info("[init] 商品搜索策略初始化完成，当前策略: {}", strategyType);
    }

    public void setProductSearchService(ProductSearchService productSearchService) {
        this.productSearchService = productSearchService;
    }

    public void setProductSearchService(String strategyType) {
        switch (strategyType) {
            case "crawler":
                this.productSearchService = applicationContext.getBean(CrawlerServiceImpl.class);
                break;
            case "oneBound":
                this.productSearchService = applicationContext.getBean(OneBoundSearchServiceImpl.class);
                break;
            default:
                log.error("[setProductSearchService] 未知的策略类型: {}", strategyType);
                throw new IllegalArgumentException("Unknown strategy type: " + strategyType);
        }
        log.info("[setProductSearchService] 切换商品搜索策略为: {}", strategyType);
    }


    /**
     * 搜索商品
     *
     * @param reqVO 搜索请求
     * @return 搜索结果
     */
    public PageResult<AppProductSpuRespVO> searchProducts(AppSearchPageReqVO reqVO){
        return productSearchService.searchProducts(reqVO);
    }

    /**
     * 根据商品链接获取详情
     *
     * @param url 商品链接
     * @return 商品详情
     */
    public AppProductSpuDetailRespVO getProductByUrl(String url){
        return productSearchService.getProductByUrl(url);
    }

    /**
     * 根据商品ID获取商品详情
     *
     * @param reqVO 详情请求
     * @return 详情
     */
    public AppProductSpuDetailRespVO getProductDetailById(AppSearchDetailReqVO reqVO){
        return productSearchService.getProductDetailById(reqVO);
    }

    /**
     * 根据商品ID获取商品详情
     *
     * @param source 来源
     * @param sourceId 来源商品ID
     * @return 详情
     */
    public AppProductSpuDetailRespVO getProductDetailBySourceId(String sourceId, String source){
        return productSearchService.getProductDetailBySourceId(sourceId, source);
    }

}
