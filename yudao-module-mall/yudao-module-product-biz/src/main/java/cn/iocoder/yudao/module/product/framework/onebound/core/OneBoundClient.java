package cn.iocoder.yudao.module.product.framework.onebound.core;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.product.framework.onebound.config.OneBoundProperties;
import cn.iocoder.yudao.module.product.framework.onebound.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.*;

/**
 * OneBound API客户端
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OneBoundClient {

    /**
     * OneBound API密钥配置键
     */
    static final String ONEBOUND_KEY_CONFIG_KEY_AND_SECRET = "oneBound.key";


    @Resource
    private OneBoundProperties oneBoundProperties;

    @Resource
    private ConfigApi configApi;

    /**
     * 淘宝商品搜索
     */
    public OneBoundSearchRespDTO searchTaobaoProducts(OneBoundSearchReqDTO reqDTO) {
        log.info("[searchTaobaoProducts] 开始调用淘宝搜索API，请求参数: {}", JsonUtils.toJsonString(reqDTO));

        String baseUrl = oneBoundProperties.getBaseUrl() + "/taobao/item_search/";
        Map<String, Object> params = buildSearchParams(reqDTO);
        String fullUrl = buildUrlWithParams(baseUrl, params);

        log.info("[searchTaobaoProducts] 完整请求URL: {}", fullUrl);

        try {
            String responseBody = executeGetRequest(fullUrl);
            log.info("[searchTaobaoProducts] API调用成功，返回信息: {}", responseBody);
            OneBoundSearchRespDTO respDTO = JsonUtils.parseObject(responseBody, OneBoundSearchRespDTO.class);

            if (!"0000".equals(respDTO.getErrorCode())) {
                log.error("[searchTaobaoProducts] API调用失败，错误码: {}, 错误信息: {}", respDTO.getErrorCode(), respDTO.getError());
                throw exception(ONEBOUND_SEARCH_FAILED, respDTO.getError());
            }

            log.info("[searchTaobaoProducts] API调用成功，返回商品数量: {}",
                    respDTO.getItems() != null && respDTO.getItems().getItem() != null ? respDTO.getItems().getItem().size() : 0);
            return respDTO;

        } catch (Exception e) {
            log.error("[searchTaobaoProducts] 调用淘宝搜索API异常", e);
            throw exception(ONEBOUND_SEARCH_FAILED, e.getMessage());
        }
    }

    /**
     * 京东商品搜索
     */
    public OneBoundSearchRespDTO searchJdProducts(OneBoundSearchReqDTO reqDTO) {
        log.info("[searchJdProducts] 开始调用京东搜索API，请求参数: {}", JsonUtils.toJsonString(reqDTO));

        String baseUrl = oneBoundProperties.getBaseUrl() + "/jd/item_search/";
        Map<String, Object> params = buildSearchParams(reqDTO);
        String fullUrl = buildUrlWithParams(baseUrl, params);

        log.info("[searchJdProducts] 完整请求URL: {}", fullUrl);

        try {
            String responseBody = executeGetRequest(fullUrl);
            log.info("[searchJdProducts] API调用成功，返回信息: {}", responseBody);
            OneBoundSearchRespDTO respDTO = JsonUtils.parseObject(responseBody, OneBoundSearchRespDTO.class);

            if (!"0000".equals(respDTO.getErrorCode())) {
                log.error("[searchJdProducts] API调用失败，错误码: {}, 错误信息: {}", respDTO.getErrorCode(), respDTO.getError());
                throw exception(ONEBOUND_SEARCH_FAILED, respDTO.getError());
            }

            log.info("[searchJdProducts] API调用成功，返回商品数量: {}",
                    respDTO.getItems() != null && respDTO.getItems().getItem() != null ? respDTO.getItems().getItem().size() : 0);
            return respDTO;

        } catch (Exception e) {
            log.error("[searchJdProducts] 调用京东搜索API异常", e);
            throw exception(ONEBOUND_SEARCH_FAILED, e.getMessage());
        }
    }

    /**
     * 淘宝商品详情
     */
    public OneBoundDetailRespDTO getTaobaoProductDetail(OneBoundDetailReqDTO reqDTO) {
        log.info("[getTaobaoProductDetail] 开始调用淘宝详情API，请求参数: {}", JsonUtils.toJsonString(reqDTO));

        String baseUrl = oneBoundProperties.getBaseUrl() + "/taobao/item_get/";
        Map<String, Object> params = buildDetailParams(reqDTO);
        String fullUrl = buildUrlWithParams(baseUrl, params);

        log.info("[getTaobaoProductDetail] 完整请求URL: {}", fullUrl);

        try {
            String responseBody = executeGetRequest(fullUrl);
            log.info("[getTaobaoProductDetail] API调用成功，返回信息: {}", responseBody);
            OneBoundDetailRespDTO respDTO = JsonUtils.parseObject(responseBody, OneBoundDetailRespDTO.class);

            if (!"0000".equals(respDTO.getErrorCode())) {
                log.error("[getTaobaoProductDetail] API调用失败，错误码: {}, 错误信息: {}", respDTO.getErrorCode(), respDTO.getError());
                throw exception(ONEBOUND_DETAIL_FAILED, respDTO.getError());
            }

            log.info("[getTaobaoProductDetail] API调用成功，商品ID: {}", respDTO.getItem() != null ? respDTO.getItem().getNumIid() : "null");
            return respDTO;

        } catch (Exception e) {
            log.error("[getTaobaoProductDetail] 调用淘宝详情API异常", e);
            throw exception(ONEBOUND_DETAIL_FAILED, e.getMessage());
        }
    }

    /**
     * 京东商品详情
     */
    public OneBoundDetailRespDTO getJdProductDetail(OneBoundDetailReqDTO reqDTO) {
        log.info("[getJdProductDetail] 开始调用京东详情API，请求参数: {}", JsonUtils.toJsonString(reqDTO));

        String baseUrl = oneBoundProperties.getBaseUrl() + "/jd/item_get/";
        Map<String, Object> params = buildDetailParams(reqDTO);
        params.put("domain_type", "jd"); // 京东特有参数
        String fullUrl = buildUrlWithParams(baseUrl, params);

        log.info("[getJdProductDetail] 完整请求URL: {}", fullUrl);

        try {
            String responseBody = executeGetRequest(fullUrl);
            log.info("[getJdProductDetail] API调用成功，返回信息: {}", responseBody);
            OneBoundDetailRespDTO respDTO = JsonUtils.parseObject(responseBody, OneBoundDetailRespDTO.class);

            if (!"0000".equals(respDTO.getErrorCode())) {
                log.error("[getJdProductDetail] API调用失败，错误码: {}, 错误信息: {}", respDTO.getErrorCode(), respDTO.getError());
                throw exception(ONEBOUND_DETAIL_FAILED, respDTO.getError());
            }

            log.info("[getJdProductDetail] API调用成功，商品ID: {}", respDTO.getItem() != null ? respDTO.getItem().getNumIid() : "null");
            return respDTO;

        } catch (Exception e) {
            log.error("[getJdProductDetail] 调用京东详情API异常", e);
            throw exception(ONEBOUND_DETAIL_FAILED, e.getMessage());
        }
    }

    /**
     * 构建搜索请求参数
     */
    private Map<String, Object> buildSearchParams(OneBoundSearchReqDTO reqDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("key", getApiKey());
        params.put("secret", getApiSecret());
        params.put("q", reqDTO.getQ());
        params.put("page", reqDTO.getPage());
        params.put("start_price", reqDTO.getStartPrice());
        params.put("end_price", reqDTO.getEndPrice());
        params.put("cat", reqDTO.getCat());
        params.put("discount_only", reqDTO.getDiscountOnly());
        params.put("sort", reqDTO.getSort());
        params.put("seller_info", reqDTO.getSellerInfo());
        params.put("nick", reqDTO.getNick());
        params.put("ppath", reqDTO.getPpath());
        params.put("imgid", reqDTO.getImgid());
        params.put("filter", reqDTO.getFilter());
        params.put("lang", reqDTO.getLang());
        return params;
    }

    /**
     * 构建详情请求参数
     */
    private Map<String, Object> buildDetailParams(OneBoundDetailReqDTO reqDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("key", getApiKey());
        params.put("secret", getApiSecret());
        params.put("num_iid", reqDTO.getNumIid());
        params.put("is_promotion", reqDTO.getIsPromotion());
        params.put("lang", reqDTO.getLang());
        return params;
    }

    /**
     * 构建带参数的完整URL
     */
    private String buildUrlWithParams(String baseUrl, Map<String, Object> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            boolean first = true;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                String key = entry.getKey();
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                urlBuilder.append(URLUtil.encode(key)).append("=").append(URLUtil.encode(value));
                first = false;
            }
        }
        return urlBuilder.toString();
    }

    /**
     * 执行GET请求（按照官方示例方式）
     */
    private String executeGetRequest(String url) throws IOException {
        URL realUrl = new URL(url);
        URLConnection conn = realUrl.openConnection();

        // 设置超时时间
        conn.setConnectTimeout(oneBoundProperties.getConnectTimeout());
        conn.setReadTimeout(oneBoundProperties.getReadTimeout());

        // 设置请求头
        conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        conn.setRequestProperty("Accept", "application/json, text/plain, */*");
        conn.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");

        try (InputStream instream = conn.getInputStream();
             BufferedReader rd = new BufferedReader(new InputStreamReader(instream, StandardCharsets.UTF_8))) {

            StringBuilder sb = new StringBuilder();
            int cp;
            while ((cp = rd.read()) != -1) {
                sb.append((char) cp);
            }
            return sb.toString();
        }
    }

    /**
     * 获取API密钥
     */
    private String getApiKey() {
        String key = configApi.getConfigValueByKey(ONEBOUND_KEY_CONFIG_KEY_AND_SECRET);
        if (StrUtil.isEmpty(key)) {
            key = oneBoundProperties.getKey();
        }
        return key.split("-")[0];
    }

    /**
     * 获取API密钥
     */
    private String getApiSecret() {
        String secret = configApi.getConfigValueByKey(ONEBOUND_KEY_CONFIG_KEY_AND_SECRET);
        if (StrUtil.isEmpty(secret)) {
            secret = oneBoundProperties.getSecret();
        }
        return secret.split("-")[1];
    }

}
