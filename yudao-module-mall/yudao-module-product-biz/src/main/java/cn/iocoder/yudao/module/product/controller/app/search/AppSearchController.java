package cn.iocoder.yudao.module.product.controller.app.search;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.http.UrlUtils;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchDetailReqVO;
import cn.iocoder.yudao.module.product.controller.app.search.vo.AppSearchPageReqVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.service.search.ProductSearchContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 商品搜索")
@RestController
@RequestMapping("/product/search")
@RequiredArgsConstructor
@Validated
@Slf4j
public class AppSearchController {

//    private final CrawlerService crawlerService;
    private final ProductSearchContext productSearchContext;

    @GetMapping("/keyword")
    @Operation(summary = "关键词搜索商品")
    @PermitAll
    public CommonResult<PageResult<AppProductSpuRespVO>> searchByKeyword(@Valid AppSearchPageReqVO reqVO) {
        log.info("[searchByKeyword] 关键词搜索开始，请求参数: {}", reqVO);

        try {
            PageResult<AppProductSpuRespVO> pageResult = productSearchContext.searchProducts(reqVO);
            log.info("[searchByKeyword] 搜索完成，结果: 总数={}, 当前页数据量={}", pageResult.getTotal(), pageResult.getList() != null ? pageResult.getList().size() : 0);

            CommonResult<PageResult<AppProductSpuRespVO>> result = success(pageResult);
            log.info("[searchByKeyword] 返回结果构建完成");
            return result;

        } catch (Exception e) {
            log.error("[searchByKeyword] 搜索异常，异常类型: {}, 异常信息: {}", e.getClass().getSimpleName(), e.getMessage(), e);
            throw e;
        }
    }

    @PostMapping("/detail")
    @Operation(summary = "根据商品ID详情")
    @PermitAll
    public CommonResult<AppProductSpuDetailRespVO> getProductDetail(AppSearchDetailReqVO reqVO) {
        log.info("[getProductDetail] 获取商品详情，请求参数: {}", reqVO);
        AppProductSpuDetailRespVO detailVO = productSearchContext.getProductDetailById(reqVO);
        return success(detailVO);
    }

    @PostMapping("/url")
    @Operation(summary = "根据商品链接获取详情")
    @PermitAll
    public CommonResult<AppProductSpuDetailRespVO> getProductDetailByUrl(String url) {
        log.info("[getProductDetail] 根据url获取商品详情，请求参数: {}", url);
        //解码 URL 参数
        String decodedUrl = UrlUtils.decodeParam(url);

        AppProductSpuDetailRespVO detailVO = productSearchContext.getProductByUrl(decodedUrl);
        return success(detailVO);
    }

    @GetMapping("/test")
    @Operation(summary = "测试爬虫系统连接")
    @PermitAll
    public CommonResult<String> testCrawlerConnection() {
        log.info("[testCrawlerConnection] 测试爬虫系统连接");

        try {
            AppSearchPageReqVO testReq = new AppSearchPageReqVO();
            testReq.setKeyword("小叶紫檀");
            testReq.setPlatform("taobao");
            testReq.setLanguage("zh");
            testReq.setPageNo(1);
            testReq.setPageSize(1);

            PageResult<AppProductSpuRespVO> result = productSearchContext.searchProducts(testReq);
            return success("连接成功，返回数据量: " + (result.getList() != null ? result.getList().size() : 0));

        } catch (Exception e) {
            log.error("[testCrawlerConnection] 连接测试失败", e);
            return success("连接失败: " + e.getMessage());
        }
    }

}
