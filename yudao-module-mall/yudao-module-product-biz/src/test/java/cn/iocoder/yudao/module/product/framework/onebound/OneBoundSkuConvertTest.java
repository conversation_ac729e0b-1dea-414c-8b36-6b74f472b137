package cn.iocoder.yudao.module.product.framework.onebound;


import cn.iocoder.yudao.module.product.controller.app.property.vo.value.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.framework.onebound.convert.OneBoundConvert;
import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundDetailRespDTO;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * OneBound SKU转换测试
 *
 * <AUTHOR>
 */
public class OneBoundSkuConvertTest {

    private final OneBoundConvert convert = OneBoundConvert.INSTANCE;

    @Test
    public void testSkuConvert() {
        // 模拟OneBound返回的SKU数据
        List<OneBoundDetailRespDTO.Sku> skuList = createMockSkuList();
        
        // 转换SKU
        List<AppProductSpuDetailRespVO.Sku> convertedSkus = new ArrayList<>();
        for (OneBoundDetailRespDTO.Sku sku : skuList) {
            AppProductSpuDetailRespVO.Sku convertedSku = convert.convertSku(sku, skuList);
            convertedSkus.add(convertedSku);
        }
        
        // 打印结果
        System.out.println("转换后的SKU数量: " + convertedSkus.size());
        for (int i = 0; i < convertedSkus.size(); i++) {
            AppProductSpuDetailRespVO.Sku sku = convertedSkus.get(i);
            System.out.println("\n=== SKU " + (i + 1) + " ===");
            System.out.println("ID: " + sku.getId());
            System.out.println("价格: " + sku.getPrice() + "分");
            System.out.println("库存: " + sku.getStock());
            System.out.println("属性:");
            if (sku.getProperties() != null) {
                for (AppProductPropertyValueDetailRespVO prop : sku.getProperties()) {
                    System.out.println("  - " + prop.getPropertyName() + " (ID:" + prop.getPropertyId() + "): " 
                                     + prop.getValueName() + " (ID:" + prop.getValueId() + ")");
                }
            }
        }
        
        // 验证属性分组
        System.out.println("\n=== 属性分组验证 ===");
        verifyPropertyGrouping(convertedSkus);
    }

    private List<OneBoundDetailRespDTO.Sku> createMockSkuList() {
        List<OneBoundDetailRespDTO.Sku> skuList = new ArrayList<>();
        
        // SKU 1: 雅黑-限时特惠, 否, 104键, 官方标配
        OneBoundDetailRespDTO.Sku sku1 = new OneBoundDetailRespDTO.Sku();
        sku1.setSkuId("1001");
        sku1.setPrice("17.90");
        sku1.setOrginalPrice("39.90");
        sku1.setQuantity("100");
        sku1.setPropertiesName("颜色分类:【雅黑-限时特惠】;是否无线:否;键数:104键;套餐类型:官方标配");
        skuList.add(sku1);
        
        // SKU 2: 白键-悬浮炫光, 否, 104键, 官方标配
        OneBoundDetailRespDTO.Sku sku2 = new OneBoundDetailRespDTO.Sku();
        sku2.setSkuId("1002");
        sku2.setPrice("19.90");
        sku2.setOrginalPrice("39.90");
        sku2.setQuantity("50");
        sku2.setPropertiesName("颜色分类:【白键-悬浮炫光】;是否无线:否;键数:104键;套餐类型:官方标配");
        skuList.add(sku2);
        
        // SKU 3: 黑键-悬浮炫光, 否, 104键, 官方标配
        OneBoundDetailRespDTO.Sku sku3 = new OneBoundDetailRespDTO.Sku();
        sku3.setSkuId("1003");
        sku3.setPrice("19.90");
        sku3.setOrginalPrice("39.90");
        sku3.setQuantity("75");
        sku3.setPropertiesName("颜色分类:【黑键-悬浮炫光】;是否无线:否;键数:104键;套餐类型:官方标配");
        skuList.add(sku3);
        
        // SKU 4: 台金-悬浮炫光, 否, 104键, 官方标配
        OneBoundDetailRespDTO.Sku sku4 = new OneBoundDetailRespDTO.Sku();
        sku4.setSkuId("1004");
        sku4.setPrice("21.90");
        sku4.setOrginalPrice("39.90");
        sku4.setQuantity("30");
        sku4.setPropertiesName("颜色分类:【台金-悬浮炫光】;是否无线:否;键数:104键;套餐类型:官方标配");
        skuList.add(sku4);
        
        // SKU 5: 雅黑-限时特惠, 否, 104键, 鼠标+送鼠标垫
        OneBoundDetailRespDTO.Sku sku5 = new OneBoundDetailRespDTO.Sku();
        sku5.setSkuId("1005");
        sku5.setPrice("25.90");
        sku5.setOrginalPrice("49.90");
        sku5.setQuantity("20");
        sku5.setPropertiesName("颜色分类:【雅黑-限时特惠】;是否无线:否;键数:104键;套餐类型:鼠标+送鼠标垫");
        skuList.add(sku5);
        
        return skuList;
    }
    
    private void verifyPropertyGrouping(List<AppProductSpuDetailRespVO.Sku> skus) {
        // 统计每个属性的不同值
        for (AppProductSpuDetailRespVO.Sku sku : skus) {
            if (sku.getProperties() != null) {
                for (AppProductPropertyValueDetailRespVO prop : sku.getProperties()) {
                    System.out.println(prop.getPropertyName() + " (ID:" + prop.getPropertyId() + "): " 
                                     + prop.getValueName() + " (ID:" + prop.getValueId() + ")");
                }
                System.out.println("---");
            }
        }
    }

}
